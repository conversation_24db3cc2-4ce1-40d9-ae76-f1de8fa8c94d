-- ===================================================================
-- SDL测试工单表结构手动迁移脚本
-- 作者: panxiaobo
-- 日期: 2025-07-20
-- 说明: 手动执行版本，请根据实际情况选择执行相应的SQL语句
-- ===================================================================

-- 第一步：检查当前表结构
-- 执行以下语句查看当前表结构
DESCRIBE sdl_test_workorder;

-- 第二步：根据检查结果选择执行相应的SQL

-- 情况1：如果表不存在，执行以下语句创建表
/*
CREATE TABLE `sdl_test_workorder` (
  `ticket_id` bigint(20) NOT NULL COMMENT '工单ID',
  `ticket_title` varchar(200) DEFAULT NULL COMMENT '工单标题',
  `ticket_type` varchar(50) DEFAULT NULL COMMENT '测试类型',
  `project_name` varchar(100) DEFAULT NULL COMMENT '所属项目',
  `system_name` varchar(100) DEFAULT NULL COMMENT '系统名称',
  `system_type` varchar(50) DEFAULT NULL COMMENT '系统类型',
  `project_manager` varchar(50) DEFAULT NULL COMMENT '项目经理',
  `software_representative` varchar(50) DEFAULT NULL COMMENT '软件代表',
  `test_applicant` varchar(50) DEFAULT NULL COMMENT '测试申请人',
  `security_representative` varchar(50) DEFAULT NULL COMMENT '安全对接人',
  `frontend_development` varchar(50) DEFAULT NULL COMMENT '前端开发',
  `backend_development` varchar(50) DEFAULT NULL COMMENT '后端开发',
  `handler` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `test_env` text COMMENT '测试环境地址',
  `test_account` text COMMENT '测试账号密码',
  `code_repo` text COMMENT '代码仓库地址',
  `trest_scope` text COMMENT '测试范围',
  `attachment` text COMMENT '附件信息',
  `expect_finish_time` date DEFAULT NULL COMMENT '期望完成时间',
  `actual_finish_time` date DEFAULT NULL COMMENT '实际完成时间',
  `priority` varchar(20) DEFAULT NULL COMMENT '优先级',
  `ticket_status` varchar(20) DEFAULT NULL COMMENT '工单状态',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ticket_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试工单表';
*/

-- 情况2：如果表存在但字段需要修改，请按顺序执行以下语句

-- 2.1 如果存在software_engineer字段，重命名为software_representative
-- ALTER TABLE sdl_test_workorder CHANGE COLUMN software_engineer software_representative varchar(50) DEFAULT NULL COMMENT '软件代表';

-- 2.2 如果不存在software_representative字段，添加该字段
-- ALTER TABLE sdl_test_workorder ADD COLUMN software_representative varchar(50) DEFAULT NULL COMMENT '软件代表' AFTER project_manager;

-- 2.3 添加frontend_development字段
-- ALTER TABLE sdl_test_workorder ADD COLUMN frontend_development varchar(50) DEFAULT NULL COMMENT '前端开发' AFTER security_representative;

-- 2.4 添加backend_development字段
-- ALTER TABLE sdl_test_workorder ADD COLUMN backend_development varchar(50) DEFAULT NULL COMMENT '后端开发' AFTER frontend_development;

-- 第三步：验证修改结果
-- 执行以下语句查看修改后的表结构
-- DESCRIBE sdl_test_workorder;

-- 第四步：测试查询（可选）
-- 执行以下语句测试查询是否正常
-- SELECT ticket_id, ticket_title, software_representative, frontend_development, backend_development FROM sdl_test_workorder LIMIT 1;

-- ===================================================================
-- 推荐的执行步骤：
-- 1. 先执行 DESCRIBE sdl_test_workorder; 查看当前表结构
-- 2. 根据实际情况，取消注释并执行相应的ALTER语句
-- 3. 执行 DESCRIBE sdl_test_workorder; 验证修改结果
-- 4. 重启应用程序测试功能
-- ===================================================================
