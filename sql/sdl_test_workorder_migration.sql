-- ===================================================================
-- SDL测试工单表结构迁移脚本
-- 作者: panxiaobo
-- 日期: 2025-07-20
-- 说明: 修改sdl_test_workorder表结构，重命名字段并新增字段
-- ===================================================================

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `sdl_test_workorder` (
  `ticket_id` bigint(20) NOT NULL COMMENT '工单ID',
  `ticket_title` varchar(200) DEFAULT NULL COMMENT '工单标题',
  `ticket_type` varchar(50) DEFAULT NULL COMMENT '测试类型',
  `project_name` varchar(100) DEFAULT NULL COMMENT '所属项目',
  `system_name` varchar(100) DEFAULT NULL COMMENT '系统名称',
  `system_type` varchar(50) DEFAULT NULL COMMENT '系统类型',
  `project_manager` varchar(50) DEFAULT NULL COMMENT '项目经理',
  `software_engineer` varchar(50) DEFAULT NULL COMMENT '软件工程师（待重命名）',
  `test_applicant` varchar(50) DEFAULT NULL COMMENT '测试申请人',
  `security_representative` varchar(50) DEFAULT NULL COMMENT '安全对接人',
  `handler` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `test_env` text COMMENT '测试环境地址',
  `test_account` text COMMENT '测试账号密码',
  `code_repo` text COMMENT '代码仓库地址',
  `trest_scope` text COMMENT '测试范围',
  `attachment` text COMMENT '附件信息',
  `expect_finish_time` date DEFAULT NULL COMMENT '期望完成时间',
  `actual_finish_time` date DEFAULT NULL COMMENT '实际完成时间',
  `priority` varchar(20) DEFAULT NULL COMMENT '优先级',
  `ticket_status` varchar(20) DEFAULT NULL COMMENT '工单状态',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ticket_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试工单表';

-- ===================================================================
-- 数据迁移步骤
-- ===================================================================

-- 1. 检查是否存在software_engineer字段，如果存在则重命名为software_representative
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sdl_test_workorder'
    AND COLUMN_NAME = 'software_engineer'
);

-- 如果software_engineer字段存在，则重命名
SET @sql = IF(@column_exists > 0,
    'ALTER TABLE sdl_test_workorder CHANGE COLUMN software_engineer software_representative varchar(50) DEFAULT NULL COMMENT ''软件代表''',
    'SELECT "software_engineer字段不存在，跳过重命名" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 检查是否存在software_representative字段，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sdl_test_workorder'
    AND COLUMN_NAME = 'software_representative'
);

-- 如果software_representative字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE sdl_test_workorder ADD COLUMN software_representative varchar(50) DEFAULT NULL COMMENT ''软件代表'' AFTER project_manager',
    'SELECT "software_representative字段已存在，跳过添加" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 检查是否存在frontend_development字段，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sdl_test_workorder'
    AND COLUMN_NAME = 'frontend_development'
);

-- 如果frontend_development字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE sdl_test_workorder ADD COLUMN frontend_development varchar(50) DEFAULT NULL COMMENT ''前端开发'' AFTER security_representative',
    'SELECT "frontend_development字段已存在，跳过添加" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 检查是否存在backend_development字段，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sdl_test_workorder'
    AND COLUMN_NAME = 'backend_development'
);

-- 如果backend_development字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE sdl_test_workorder ADD COLUMN backend_development varchar(50) DEFAULT NULL COMMENT ''后端开发'' AFTER frontend_development',
    'SELECT "backend_development字段已存在，跳过添加" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ===================================================================
-- 验证表结构
-- ===================================================================

-- 显示最终的表结构
DESCRIBE sdl_test_workorder;

-- 显示修改完成的消息
SELECT 'SDL测试工单表结构迁移完成！' AS message;
SELECT '已完成以下修改：' AS message;
SELECT '1. software_engineer字段重命名为software_representative' AS modification;
SELECT '2. 新增frontend_development字段（前端开发）' AS modification;
SELECT '3. 新增backend_development字段（后端开发）' AS modification;
