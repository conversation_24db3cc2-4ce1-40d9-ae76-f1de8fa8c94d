-- ===================================================================
-- SDL测试工单表结构简化迁移脚本
-- 作者: panxiaobo
-- 日期: 2025-07-20
-- 说明: 简化版本的数据库迁移脚本，直接执行ALTER语句
-- ===================================================================

-- 如果表不存在，先创建基础表结构
CREATE TABLE IF NOT EXISTS `sdl_test_workorder` (
  `ticket_id` bigint(20) NOT NULL COMMENT '工单ID',
  `ticket_title` varchar(200) DEFAULT NULL COMMENT '工单标题',
  `ticket_type` varchar(50) DEFAULT NULL COMMENT '测试类型',
  `project_name` varchar(100) DEFAULT NULL COMMENT '所属项目',
  `system_name` varchar(100) DEFAULT NULL COMMENT '系统名称',
  `system_type` varchar(50) DEFAULT NULL COMMENT '系统类型',
  `project_manager` varchar(50) DEFAULT NULL COMMENT '项目经理',
  `software_engineer` varchar(50) DEFAULT NULL COMMENT '软件工程师',
  `test_applicant` varchar(50) DEFAULT NULL COMMENT '测试申请人',
  `security_representative` varchar(50) DEFAULT NULL COMMENT '安全对接人',
  `handler` varchar(50) DEFAULT NULL COMMENT '当前处理人',
  `test_env` text COMMENT '测试环境地址',
  `test_account` text COMMENT '测试账号密码',
  `code_repo` text COMMENT '代码仓库地址',
  `trest_scope` text COMMENT '测试范围',
  `attachment` text COMMENT '附件信息',
  `expect_finish_time` date DEFAULT NULL COMMENT '期望完成时间',
  `actual_finish_time` date DEFAULT NULL COMMENT '实际完成时间',
  `priority` varchar(20) DEFAULT NULL COMMENT '优先级',
  `ticket_status` varchar(20) DEFAULT NULL COMMENT '工单状态',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ticket_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试工单表';

-- 方案1：如果表中已有software_engineer字段，重命名为software_representative
-- ALTER TABLE sdl_test_workorder CHANGE COLUMN software_engineer software_representative varchar(50) DEFAULT NULL COMMENT '软件代表';

-- 方案2：如果表中没有software_representative字段，直接添加
ALTER TABLE sdl_test_workorder ADD COLUMN IF NOT EXISTS software_representative varchar(50) DEFAULT NULL COMMENT '软件代表' AFTER project_manager;

-- 添加新字段：前端开发
ALTER TABLE sdl_test_workorder ADD COLUMN IF NOT EXISTS frontend_development varchar(50) DEFAULT NULL COMMENT '前端开发' AFTER security_representative;

-- 添加新字段：后端开发
ALTER TABLE sdl_test_workorder ADD COLUMN IF NOT EXISTS backend_development varchar(50) DEFAULT NULL COMMENT '后端开发' AFTER frontend_development;

-- 如果原来有software_engineer字段，可以选择删除（请谨慎操作）
-- ALTER TABLE sdl_test_workorder DROP COLUMN IF EXISTS software_engineer;

-- 查看最终表结构
DESCRIBE sdl_test_workorder;
